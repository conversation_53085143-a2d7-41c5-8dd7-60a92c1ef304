import express, { <PERSON><PERSON><PERSON>and<PERSON> } from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import json2xls from 'json2xls';
import apiV1UnAuthRoutes from './routes/unAuthRoute';
import apiV1AuthRoutes from './routes/authRoute';
import { logger, accessSuccessLogger, accessErrorLogger } from './utils/logger';
import { tokenHandler } from './middlewares';
import multer from 'multer';
import path from 'path';
import mediaRoutes from './routes/upload/mediaRoutes';
import uploadHandler from './controllers/admin/upload';
import stockOffers from './controllers/user/stock_offers';
import vendorOrders from './controllers/admin/vendor_orders';
import stockMargin from './controllers/admin/stock_margin';
import applePayRefund from './controllers/user/authorize_payment/apple_pay_refund_cron';
import { httpStatusCodes } from './utils/constants';
import cron from 'node-cron';

const upload = multer();
// import uploadHandler from './controllers/admin/upload';

const app = express();

// register loggers
app.use(accessSuccessLogger);
app.use(accessErrorLogger);
app.use(express.static(path.join(__dirname, 'public')));

app.disable('x-powered-by');
app.use(express.json() as RequestHandler);
app.use(express.urlencoded({ extended: true }) as RequestHandler);
app.use(json2xls.middleware);

app.use(
    cors({
        credentials: true,
        origin: [
            'http://localhost:2807',
            'https://diamond-company.panel.dharmatech.in',
            'https://admin.diamondcompany.com',
            'https://vendor.diamondcompany.com',
            'https://web.diamondcompany.com',
            'http://*************:3004',
            'http://*************:3002'
        ],
        methods: ['GET', 'POST', 'PUT', 'DELETE']
    })
);
app.use(cookieParser('CookieSecret'));

app.all('/*', (req, res, next) => {
    // 	// CORS headers
    res.header('Access-Control-Allow-Origin', '*'); // restrict it to the required domain
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
    // Set custom headers for CORS
    res.header('Access-Control-Allow-Headers', 'Content-type,Accept,X-Access-Token,X-Key,Authorization,Client-Key');

    if (req.method === 'OPTIONS') {
        res.status(httpStatusCodes.SUCCESS_CODE).end();
    } else {
        next();
    }
});

global.stocksUpdatedId = [];

// app.use('/file/upload', uploadHandler);
app.use('/file/upload', uploadHandler);

// parse FormData
app.use(upload.array());

app.use('/api/v1', apiV1UnAuthRoutes);
app.use('/api/v1/auth', tokenHandler, apiV1AuthRoutes);
/*
* (req,res,next)=>{
    /**
     * If Not super admin throw error
     *
     * }*/
app.use('/', mediaRoutes);

/**
 * Reject out dated offers
 * 12:00 AM every Day
 */
cron.schedule('00 00 * * *', stockOffers.rejectOffers);

/**
 * Apple pay refund cron
 * 12:00 AM every Day
 */
cron.schedule('00 00 * * *', applePayRefund.applePayRefundCron);

/**
 * Raise invoice notification cron
 * 12:00 AM every Day
 */
cron.schedule('00 00 * * *', vendorOrders.raiseInvoiceCron);

// setTimeout(() => {
//     stockMargin.updateStockMargin();
// }, 1000)

// global error handler
// TODO: save error in one file and sending mail on error
app.use((err, req, res, next) => {
    logger.error(`!!!!!!Global Error!!!!!!! ${err.stack}`);

    res.status(500).json({
        status: 500,
        message:
            typeof err === 'string' ? err : typeof err?.message === 'string' ? err?.message : 'Internal Server Error',
        error: err
    });
});

export = app;
