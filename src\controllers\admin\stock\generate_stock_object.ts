import models from '../../../models';
import { getStockModalFields } from './getStockModelFields';
import { naturalAlias, stockStatus } from '../../../utils/constants';
import moment from 'moment';
import { logger } from '../../../utils/logger';
import { Op } from 'sequelize';

/// add stocks
export async function generateStockObject(
    convertedResultArray: any,
    stockData: any,
    stockIds: string[],
    offset: number,
    adminId: string | null,
    vendorId: string | null,
    stockMargins: any,
    vendorMargin: any,
    requestIp: string
) {
    const newStockIds = [...stockIds];
    const newStockData: any[] = stockData
        .filter((stockItem: any) => newStockIds.includes(stockItem.stock_id))
        .map((stock: any) => {
            return {
                stock_id: stock.stock_id,
                certificate_number: stock.certificate_number,
                status: stock.status,
                id: stock.id,
                is_offer_created: stock.is_offer_created
            };
        });

    const stockList: any = [];
    const updatedStockList: any = [];
    const stocksWithZeroPrice: any = [];
    const stocksWithoutCertificate: any = [];
    const stocksWithoutLab: any = [];
    const stocksWithNaturalDiamond: any = [];

    for (const item of convertedResultArray) {
        const stockCreateObject: any = await getStockModalFields(item, offset, null, vendorId, stockMargins, vendorMargin);

        // if (['none', 'nan', 'non', ''].includes(String(stockCreateObject?.lab).trim().toLowerCase())) {
        //     continue;
        // }

        if (stockCreateObject) {
            const foundedStockData: any = newStockData.find((stockItem: any) => {
                /// same stock ids
                if (stockItem.stock_id === stockCreateObject.stock_id) {
                    return stockItem;
                }

                if (
                    stockCreateObject.certificate_number &&
                    stockItem.certificate_number &&
                    stockCreateObject.certificate_number === stockItem.certificate_number
                ) {
                    return stockItem;
                }

                return;
            });

            if (foundedStockData) {
                if (
                    !stockCreateObject?.final_price ||
                    !stockCreateObject?.final_price_ori ||
                    !stockCreateObject?.price_per_caret
                ) {
                    stocksWithZeroPrice.push(stockCreateObject);
                } else if (!stockCreateObject?.certificate_number) {
                    stocksWithoutCertificate.push(stockCreateObject);
                } else if (naturalAlias.includes(String(stockCreateObject?.growth_type).trim().toLowerCase())) {
                    stocksWithNaturalDiamond.push(stockCreateObject);
                } else if (
                    !stockCreateObject?.lab ||
                    ['none', 'nan', 'non', ''].includes(String(stockCreateObject?.lab).toLocaleLowerCase().trim())
                ) {
                    stocksWithoutLab.push(stockCreateObject);
                } else {
                    // update query
                    if (
                        [stockStatus.available, stockStatus.returned].includes(foundedStockData.status) &&
                        !foundedStockData.is_offer_created
                    ) {
                        /// make status available
                        stockCreateObject.status =
                            foundedStockData.status === stockStatus.returned
                                ? stockStatus.available
                                : stockCreateObject.status;

                    if([stockStatus.onHold, stockStatus.onMemo].includes(stockCreateObject.status)) {
                            // if updated status is on hold then destroy that stock
                            await models.stocks.destroy({
                                where: {
                                    id: {
                                        [Op.in]: [foundedStockData.id]
                                    },
                                    status: {
                                        [Op.in]: [stockStatus.onHold, stockStatus.onMemo]
                                    }
                                },
                            });

                            const { id, updatedAt, createdAt, ...rest } = JSON.parse(JSON.stringify(foundedStockData)); // Destructure and exclude 'id'
                            await models.deleted_stocks.create({
                                ...rest, // Spread the rest of the properties
                                stock_uuid: id, // Use the 'id' value as 'stock_uuid'
                                deleted_by_id: adminId || vendorId, // Set the deleted_by_id
                                deleted_by_ip: requestIp,
                                delete_type: 'Add Stock From File PATH',
                                createdAt: moment.utc(),
                                updatedAt: moment.utc()
                            });
                        } else {
                            /// update available and returned stock
                            await models.stocks.update(
                                { ...stockCreateObject, updatedAt: moment.utc() },
                                {
                                    where: {
                                        id: foundedStockData.id
                                    }
                                }
                            );

                            updatedStockList.push(foundedStockData?.stock_id);
                        }
                    }
                }
            } else {
                stockList.push(stockCreateObject);
                newStockIds.push(stockCreateObject.stock_id);
                newStockData.push({
                    stock_id: stockCreateObject.stock_id,
                    certificate_number: stockCreateObject.certificate_number
                });
            }
        }
    }

    return {
        stock_object_list: stockList,
        updated_stock_list: updatedStockList,
        stocksWithZeroPrice,
        stocksWithoutCertificate,
        stocksWithoutLab,
        stocksWithNaturalDiamond
    };
}
