import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger';
import {
    adminRole,
    buyRequestType,
    httpStatusCodes,
    NotificationType,
    orderStatus,
    PaymentMode,
    ReturnOrderStatus,
    vendorOrderStatus
} from '../../utils/constants';
import models, { sequelize } from '../../models';
import { Op, Transaction } from 'sequelize';
import authorizePayment from '../user/authorize_payment/authorize_payment';
import userNotifications from '../user/user_notifications/user_notification';
class ReturnOrder {
    /**
     * @api {post} /v1/auth/admin/return-order
     * @apiName createReturnOrder
     * @apiGroup ReturnOrder
     *
     *
     * @apiSuccess {Object} ReturnOrder.
     */
    async createReturnOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!return order function start!!!!!');
        const transaction = await sequelize.transaction();
        try {
            const { return_stock_ids, order_id } = req.body;
            const reqId = req[`id`];
            const role = req[`role`];

            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                throw new Error(`Unauthorized access!!!`);
            }

            const order: any = await models.orders.findOne({ where: { id: order_id } });

            if (!order) {
                throw new Error('Order not found!!');
            }

            if (order.order_status !== orderStatus.pending) {
                throw new Error('Order already shipped!!');
            }

            /// requested stock ids
            const stockIds: any = return_stock_ids?.map((item: any) => item?.stock_id);

            /// fetch vendor orders
            const vendorOrders: any = await models.vendor_orders.findAll({
                where: { order_id }
            });

            if (vendorOrders.length) {
                /// check all vendor orders are pending
                const isVendorOrdersPending = JSON.parse(JSON.stringify(vendorOrders))?.every(
                    (item: any) => item.status === vendorOrderStatus.pending
                );

                if (!isVendorOrdersPending) {
                    throw new Error('Vendor orders are not pending!!!');
                }

                /// returned vendor orders
                const toBeReturnedVendorOrders: any = JSON.parse(JSON.stringify(vendorOrders)).filter((item: any) =>
                    stockIds.includes(item?.stock_id)
                );

                /// to be returned vendor orders ids
                const toBeReturnedVendorOrderIds: any = toBeReturnedVendorOrders.map((item: any) => item?.id);

                /// update all vendor order status to CANCELED which are returned by admin
                await models.vendor_orders.update(
                    { status: vendorOrderStatus.canceled },
                    { where: { id: { [Op.in]: toBeReturnedVendorOrderIds } }, transaction }
                );

                /// create vendor order trails
                /// vendor order trails items
                const vendorOrdersTrailsData: any[] = toBeReturnedVendorOrders.map((vendorOrder: any) => ({
                    vendor_order_id: vendorOrder.id,
                    order_id: vendorOrder.order_id,
                    buy_request_id: vendorOrder.buy_request_id,
                    vendor_id: vendorOrder.vendor_id,
                    updated_by_id: reqId,
                    payload: JSON.stringify(vendorOrder),
                    status: vendorOrderStatus.canceled
                }));

                /// create vendor order trail
                await models.vendor_order_trails.bulkCreate(vendorOrdersTrailsData, { transaction });
            }

            /// fetch buy request details
            const buyRequestDetails: any = await models.buy_requests.findOne({ where: { id: order?.buy_request_id } });

            if (!buyRequestDetails) {
                throw new Error('Buy request not found!!!');
            }

            /// order stockIds
            const orderStockIds: any = buyRequestDetails?.stock_ids?.map((item: any) => item?.stock_id);

            /// check requested stock ids are part of order stock ids
            const isStockIdsValid = stockIds?.every((stockId: any) => orderStockIds.includes(stockId));

            /// check stock ids are valid
            if (!isStockIdsValid) {
                throw new Error('Invalid stock ids!!!');
            }

            const stocks: any = await models.return_orders.findAll({ where: { stock_id: { [Op.in]: stockIds } } });

            if (stocks.length) {
                throw new Error('Stock already exists in return list!!!');
            }

            const bulkCreateObject: any[] = return_stock_ids.map((item: any) => ({
                order_id,
                user_id: order.user_id,
                stock_id: item.stock_id,
                vendor_id: item?.vendor_id,
                admin_id: item?.admin_id,
                status: item?.admin_id ? 'ACCEPTED' : 'PENDING',
                reason: item?.reason,
                is_accepted: false,
                is_action_taken: false,
                returned_by_type: 'ADMIN',
                returned_by_id: reqId
            }));

            logger.info('Creating return orders');
            const createdReturnOrders: any = await models.return_orders.bulkCreate(bulkCreateObject, { transaction });

            /// accept return order when created by admin
            for (const returnOrder of JSON.parse(JSON.stringify(createdReturnOrders))) {
                /// add data to req object to accept return order
                req.body.return_order_id = returnOrder.id;
                req.body.is_accepted = true;

                /// call function to accept return order
                await new ReturnOrder().returnOrderAction(req, res, next, transaction);
            }

            /// create order tails with return order
            const orderPayload: any = {
                ...JSON.parse(JSON.stringify(order)),
                return_order: JSON.parse(JSON.stringify(createdReturnOrders))
            };

            /// create order trail
            await models.order_trails.create(
                {
                    order_id: order?.id,
                    user_id: order?.user_id,
                    buy_request_id: order?.buy_request_id,
                    updated_by_id: reqId,
                    payload: JSON.stringify(orderPayload),
                    payment_status: order?.payment_status, // pending, paid, failed, canceled
                    order_status: order?.order_status // pending, processing, shipped, delivered, canceled
                },
                { transaction }
            );

            /// commit transaction
            await transaction.commit();

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Return order initiated successfully'
            });

            try {
                /// return order initiated email to user
                userNotifications.returnRequestInitiatedNotificationToUser(order_id);
            } catch (error: any) {
                logger.error(error);
            }

            return;
        } catch (error: any) {
            /// rollback transaction
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/return-order
     * @apiName listReturnOrder
     * @apiGroup ReturnOrder
     *
     *
     * @apiSuccess {Object} ReturnOrder.
     */
    async listReturnOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!list return order function start!!!!!');
        try {
            let skip: any = req.query.skip;
            let limit: any = req.query.limit;
            const role = req[`role`];
            const id = req[`id`];
            const isActionTaken = req.query.is_action_taken;
            const isAccepted = req.query.is_accepted;
            const list_vendor_stocks = req.query.list_vendor_stocks;
            const conditions: any = [];

            if (skip && limit) {
                skip = parseInt(String(req.query.skip), 10);
            }
            if (skip && limit) {
                limit = parseInt(String(req.query.limit), 10);
            }

            if (role === adminRole.vendor) {
                conditions.push({ vendor_id: id });
            }

            if (list_vendor_stocks?.toString().toLowerCase() === 'true') {
                conditions.push({ vendor_id: { [Op.ne]: null } });
            }

            if (isActionTaken) {
                conditions.push({ is_action_taken: isActionTaken });
            }

            if (isAccepted) {
                conditions.push({ is_accepted: isAccepted });
            }

            const { rows, count } = await models.return_orders.findAndCountAll({
                where: { [Op.and]: conditions },
                include: [
                    { model: models.orders, attributes: ['id', 'order_code', 'type'] },
                    { model: models.users, attributes: ['id', 'first_name', 'last_name', 'email'] },
                    { model: models.vendors, attributes: ['id', 'first_name', 'last_name', 'email', 'company_name'], required: false },
                    {
                        model: models.stocks,
                        attributes: [
                            'id',
                            'stock_id',
                            'status',
                            'weight',
                            'color',
                            'clarity',
                            'shape',
                            'cut',
                            'polish',
                            'symmetry',
                            'fluorescence_intensity',
                            'discounts',
                            'discounts_ori',
                            'price_per_caret',
                            'price_per_caret_ori',
                            'final_price',
                            'final_price_ori',
                            'admin_id',
                            'vendor_id',
                            'sku_number',
                            'stock_margin'
                        ]
                    }
                ],
                order: [['updatedAt', 'DESC']],
                offset: skip,
                limit
            });

            for (const returnOrder of rows) {

                /// update approved margin for vendor
                const vendorOrder: any = await models.vendor_orders.findOne({
                    where: {
                        order_id: returnOrder?.order?.id,
                        vendor_id: returnOrder?.vendor_id,
                        stock_id: returnOrder?.stock_id
                    }
                });

                if (vendorOrder) {
                    if (vendorOrder?.approved_margin?.stock?.stock_margin) {
                        returnOrder.stock.stock_margin = vendorOrder?.approved_margin?.stock?.stock_margin;
                    }
                    if (vendorOrder?.approved_margin?.stock?.final_price_ori) {
                        returnOrder.stock.final_price_ori = vendorOrder?.approved_margin?.stock?.final_price_ori;
                    }
                    if (vendorOrder?.approved_margin?.stock?.price_per_caret_ori) {
                        returnOrder.stock.price_per_caret_ori = vendorOrder?.approved_margin?.stock?.price_per_caret_ori;
                    }
                }



                /// refund stock offer price
                if (returnOrder?.order?.type === buyRequestType.offer) {
                    /// fetch stock offer
                    const offer: any = await models.stock_offers.findOne({
                        where: { order_id: returnOrder?.order?.id }
                    });

                    if (!offer) {
                        throw new Error('Offer not found!!!');
                    }
                    if (role === adminRole.vendor) {
                        returnOrder.stock.final_price = offer?.updated_offer_price;
                        returnOrder.stock.price_per_caret = offer?.updated_offer_price / returnOrder?.stock?.weight;
                        returnOrder.stock.final_price_ori = offer?.updated_offer_price;
                        returnOrder.stock.price_per_caret_ori = offer?.updated_offer_price / returnOrder?.stock?.weight;
                    } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                        returnOrder.stock.final_price = offer?.offer_price;
                        returnOrder.stock.price_per_caret = offer?.offer_price / returnOrder?.stock?.weight;
                    }
                }
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Return orders successfully listed',
                data: rows,
                count
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/return-order-action
     * @apiName returnOrderAction
     * @apiGroup ReturnOrder
     *
     *
     * @apiSuccess {Object} ReturnOrder.
     */
    async returnOrderAction(req: Request, res: Response, next: NextFunction, adminTransaction?: Transaction) {
        logger.info('!!!!!!return order action function start!!!!!');

        const transaction = adminTransaction ?? (await sequelize.transaction());

        try {
            const role = req[`role`];
            const { is_accepted, return_order_id, reject_reason } = req.body;
            const updateObject: any = {};

            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                throw new Error(`Unauthorized access!!!`);
            }

            if (is_accepted.toString().toLowerCase() === 'false') {
                if (!reject_reason) {
                    throw new Error(`reject_reason required!!!`);
                }
            }

            const returnOrder: any = await models.return_orders.findOne({
                where: { id: return_order_id },
                include: [
                    {
                        model: models.orders,
                        attributes: ['id', 'buy_request_id', 'type'],
                        include: [
                            {
                                model: models.buy_requests,
                                attributes: [
                                    'id',
                                    'status',
                                    'user_id',
                                    'payment_mode',
                                    'trans_id',
                                    'ref_trans_id',
                                    'card_number',
                                    'exp_date',
                                    'authorized_amount',
                                    'type'
                                ]
                            }
                        ]
                    }
                ],
                transaction
            });

            if (!returnOrder) {
                throw new Error('Not found!!!');
            }

            if (returnOrder.is_accepted) {
                throw new Error('Return order already accepted!!');
            }

            //// ACCEPTING return order
            if (is_accepted.toString().toLowerCase() === 'true') {
                /// refund stock offer price
                if (returnOrder?.order?.type === buyRequestType.offer) {
                    logger.info('accepting return order for type offer');
                    /// fetch stock offer
                    const offer: any = await models.stock_offers.findOne({
                        where: { order_id: returnOrder?.order?.id }
                    });

                    if (!offer) {
                        throw new Error('Offer not found!!!');
                    }
                    updateObject.refund_amount = offer?.offer_price;
                    returnOrder.refund_amount = offer?.offer_price;
                }
                /// refund normal price
                else {
                    logger.info('accepting return normal order');
                    /// fetch stock
                    const stock: any = await models.stocks.findOne({ where: { id: returnOrder?.stock_id } });

                    /// refund amount for refund and admin panel
                    updateObject.refund_amount = stock?.final_price;
                    returnOrder.refund_amount = stock?.final_price;

                    /// find approved margin for vendor
                    const vendorOrder: any = await models.vendor_orders.findOne({
                        where: {
                            order_id: returnOrder?.order?.id,
                            vendor_id: returnOrder?.vendor_id,
                            stock_id: returnOrder?.stock_id,
                        }
                    });

                    /// vendor refund amount for vendor panel
                    updateObject.vendor_refund_amount = vendorOrder?.approved_margin?.stock?.final_price_ori ?? stock?.final_price_ori;
                    returnOrder.vendor_refund_amount = vendorOrder?.approved_margin?.stock?.final_price_ori ?? stock?.final_price_ori;
                }
            }

            updateObject.is_accepted = is_accepted;
            updateObject.is_action_taken = true;
            updateObject.reject_reason = reject_reason;

            /// admins stock mark received when accepted
            if (is_accepted.toString().toLowerCase() === 'true' && returnOrder?.admin_id) {
                updateObject.status = ReturnOrderStatus.received;
            }

            logger.info('Updating return order status into table!!!');
            /// update is accepted
            await models.return_orders.update(updateObject, { where: { id: return_order_id }, transaction });

            /// update stock status to RETURNED
            if (is_accepted.toString().toLowerCase() === 'true') {
                await models.stocks.update(
                    { status: 'RETURNED' },
                    { where: { id: returnOrder.stock_id }, transaction }
                );
            }

            if (!returnOrder?.order) {
                throw new Error('Order not found!!!');
            }

            if (!returnOrder?.order?.buy_request) {
                throw new Error('Buy request not found!!!');
            }

            /// initiate refund
            if (is_accepted.toString().toLowerCase() === 'true') {
                logger.info('initiating refund amount on return accepted!!!');
                logger.info(`authorized_amount: ${returnOrder?.order?.buy_request?.authorized_amount}`);
                logger.info(`refund_amount: ${updateObject.refund_amount}`);
                /// void transaction
                if (returnOrder?.order?.buy_request?.payment_mode === PaymentMode.creditCard) {
                    /// check refund amount is less than authorized amount
                    if (
                        parseFloat(parseFloat(returnOrder?.order?.buy_request?.authorized_amount).toFixed(2)) >=
                        parseFloat(parseFloat(updateObject.refund_amount).toFixed(2))
                    ) {
                        /// refund transaction
                        await authorizePayment.refundTransaction(
                            updateObject.refund_amount,
                            returnOrder?.order?.buy_request,
                            transaction
                        );
                    } else {
                        throw new Error('Refund amount exceeded authorized amount!!!');
                    }
                } /// restore credit limit
                else if (returnOrder?.order?.buy_request?.payment_mode === PaymentMode.creditLimit) {
                    /// credit refund amount when return order accepted
                    if (updateObject.refund_amount) {
                        logger.info('re-credit credit-limit on return order accepted');
                        /// make credit entry
                        await models.credit_histories.create(
                            {
                                user_id: returnOrder?.order?.buy_request?.user_id,
                                credit: parseFloat(parseFloat(updateObject.refund_amount).toFixed(2)),
                                type: 'RETURN-ORDER',
                                transaction_type: 'CREDIT',
                                buy_request_id: returnOrder?.order?.buy_request?.id
                            },
                            { transaction }
                        );
                    }
                } else if (returnOrder?.order?.buy_request?.payment_mode === PaymentMode.applePay) {
                    /// check refund amount is less than authorized amount
                    if (
                        parseFloat(parseFloat(returnOrder?.order?.buy_request?.authorized_amount).toFixed(2)) >=
                        parseFloat(parseFloat(updateObject.refund_amount).toFixed(2))
                    ) {
                        /// refund transaction
                        await authorizePayment.refundTransaction(
                            updateObject.refund_amount,
                            returnOrder?.order?.buy_request,
                            transaction
                        );
                    } else {
                        throw new Error('Refund amount exceeded authorized amount!!!');
                    }
                }
            }

            /// for admin accepting multiple return orders
            /// adminTransaction will be undefined on vendor take action on single return order
            if (!adminTransaction) {
                await transaction.commit();

                res.json({
                    status: httpStatusCodes.SUCCESS_CODE,
                    message: 'Return order status updated'
                });
            }

            try {
                /// return order accepted notifications
                if (is_accepted.toString().toLowerCase() === 'true') {
                    userNotifications.sendReturnOrderAcceptedNotification(
                        NotificationType.returnOrderAccepted,
                        returnOrder.id
                    );
                } else if (is_accepted.toString().toLowerCase() === 'false') {
                    userNotifications.sendReturnOrderAcceptedNotification(
                        NotificationType.returnOrderRejected,
                        returnOrder.id
                    );
                }
            } catch (error: any) {
                logger.error(error);
            }

            try {
                /// send credit limit added notifications when return order accepted
                if (is_accepted.toString().toLowerCase() === 'true') {
                    if (returnOrder?.order?.buy_request?.payment_mode === PaymentMode.creditLimit) {
                        /// send notification when credit added
                        userNotifications.sendCreditLimitNotifications(
                            NotificationType.creditLimitAdded,
                            returnOrder?.order?.buy_request?.user_id
                        );
                    }
                }
            } catch (error: any) {
                logger.error(error);
            }

            return;
        } catch (error: any) {
            await transaction?.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/return-order-status
     * @apiName returnOrderStatus
     * @apiGroup ReturnOrder
     *
     *
     * @apiSuccess {Object} ReturnOrder.
     */
    async returnOrderStatus(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!return order status function start!!!!!');
        const transaction = await sequelize.transaction();

        try {
            const role = req[`role`];
            const { status, return_order_id, received_amount } = req.body;
            const updateObject: any = {};

            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                throw new Error(`Unauthorized access!!!`);
            }

            const returnOrder: any = await models.return_orders.findOne({
                where: { id: return_order_id },
                transaction
            });

            if (!returnOrder) {
                throw new Error('Not found!!!');
            }

            if (status === ReturnOrderStatus.received) {
                if (!received_amount) {
                    throw new Error('received_amount required');
                }
                updateObject.received_amount = received_amount;
            }

            updateObject.status = status;

            logger.info(`Updating return orders ${status}`);

            /// update return order
            await models.return_orders.update(updateObject, { where: { id: return_order_id }, transaction });

            /// update stock status to RETURNED
            if (status === ReturnOrderStatus.received) {
                logger.info('Updating stock status to RETURNED');
                await models.stocks.update(
                    { status: 'RETURNED' },
                    { where: { id: returnOrder.stock_id }, transaction }
                );
            }

            await transaction.commit();

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Return order status updated'
            });

            return;
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }
}

export default new ReturnOrder();
