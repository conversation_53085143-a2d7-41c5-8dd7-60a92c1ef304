import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface PolicyViolationsAttributes {
    id?: string;
    vendor_id: string;
    violation_type: string; // DUPLICATE_DIAMOND, UNFULFILLED_ORDER, LATE_SHIPMENT
    stock_id?: string;
    order_id?: string;
    details?: object;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface PolicyViolationsCreationAttributes extends Optional<PolicyViolationsAttributes, 'id'> {}

interface PolicyViolationsInstance extends Model<PolicyViolationsAttributes, PolicyViolationsCreationAttributes>, PolicyViolationsAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type PolicyViolationsStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => PolicyViolationsInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const policy_violations = sequelize.define<PolicyViolationsInstance>(
        'policy_violations',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            title: {
                type: DataTypes.STRING,
                allowNull: false
            },
            description: {
                type: DataTypes.STRING,
                allowNull: false
            },
            amount: {
                type: DataTypes.DOUBLE,
                allowNull: false
            },
            countries: {
                type: DataTypes.ARRAY(DataTypes.STRING),
                allowNull: false
            },
            is_default: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as PolicyViolationsStatic;

    // TODO: make common function to sync
    // await policy_violations.sync({ alter: true });

    return policy_violations;
};
